import { LeafletModule } from '@asymmetrik/ngx-leaflet';
import { SimplebarAngularModule } from 'simplebar-angular';
import { NgModule } from '@angular/core';
import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { HasAnyAuthorityDirective } from './directives/has-any-authority.directive';
import { LoaderComponent } from './loader/loader.component';
import { AlertComponent } from './alert/alert.component';
import { SharedRoutingModule } from './shared-routing.module';
import { ConfirmComponent } from './confirm/confirm.component';
import { InputReaderComponent } from './input-reader/input-reader.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { PdfViewerComponent } from './pdf-viewer/pdf-viewer.component';
import { AccountEditComponent } from './account-edit/account-edit.component';
import {
    NgbAlertModule,
    NgbPaginationModule,
    NgbTypeaheadModule,
    NgbToastModule,
    NgbModalModule,
    NgbDropdownModule, NgbDatepickerModule, NgbNavModule, NgbCollapseModule
} from '@ng-bootstrap/ng-bootstrap';
import { Decimal2CorrectDirective } from './directives/decimal2-correct.directive';
import { AutoApplyAuthoritiesDirective } from './directives/auto-apply-authorities.directive';

import { TruncatePipe } from './pipe/truncate.pipe';

import { InjectableRxStompConfig, RxStompService, rxStompServiceFactory } from '@stomp/ng2-stompjs';
import { myRxStompConfig } from './my-rx-stomp.config';
import { FormReadonlyModeDirective } from './directives/form-readonly-mode.directive';


import { TypeaheadScrollFixDirective } from './directives/typeahead-scroll-fix.directive';
import { ValidationOnBlurDirective } from './directives/validation-on-blur.directive';
import { ProtectedButtonLvl2Directive } from './directives/protected-button-lvl2.directive';
import { AuthListenerComponent } from './auth-listener/auth-listener.component';
import { AuthorityNamePipe } from './pipe/authority-name.pipe';
import { TypeaheadSharedDirective } from './directives/typeahead-shared.directive';
import { TimePipe } from './pipe/time.pipe';
import { SafeHtmlPipe } from './pipe/safe-html.pipe';
import { AuditPipe } from './pipe/audit.pipe';
import { ReadOnlyDirective } from './directives/read-only.directive';
import { ErrorModalComponent } from './error-modal/error-modal.component';
import { AuditComponent } from './audit/audit.component';
import { GridModule } from '@progress/kendo-angular-grid';
import { SwitchComponent } from './switch/switch.component';
import { PracticeComponent } from './practice/practice.component';
import { NumberInputSubmitEventTriggerDirective } from './directives/number-input-submit-event-trigger.directive';
import { NumberInputFormaterDirective } from './directives/number-input-formater.directive';
import { DraggableOrderedListInputComponent } from './draggable-ordered-list-input/draggable-ordered-list-input.component';
import { DragulaModule, DragulaService } from 'ng2-dragula';
import { FiltersModalComponent } from "./filters/filters-modal-service/filters-modal/filters-modal.component";
import { TypeaheadComponent } from "./typeahead/typeahead.component";
import { FilteringButtonComponent } from "./filters/filtering-button/filtering-button.component";
import { Select2Module } from "ng-select2-component";
import { ActionIconComponent } from './action-icon/action-icon.component';
import { ExportPdfModalComponent } from './export/export-pdf-modal/export-pdf-modal.component';
import { ExportPdfComponent } from './export/export-pdf/export-pdf.component';
import { DatePickerComponent } from './date-picker/date-picker.component';
import { NgxMaskModule } from 'ngx-mask';
import { FormControlErrorDirective, ValidatorErrorPipe } from './directives/form-control-error.directive';
import { NativeElementInjectorDirective } from './directives/native-element-injector.directive';
import { PhoneNumberComponent } from './phone-number/phone-number.component';
import { UploadFileModalComponent } from './upload-file/upload-file-modal/upload-file-modal.component';
import { ButtonUploadFileComponent } from './upload-file/button-upload-file/button-upload-file.component';
import { FileUploadedComponent } from './upload-file/file-uploaded/file-uploaded.component';
import { DragAndDropDirective } from './directives/drag-and-drop.directive';
import { PeriodSetterComponent } from './period-setter/period-setter.component';
import { AllowLettersOnlyDirective } from './directives/allowLettersOnly.directive';
import { AutoFocusDirective } from './directives/auto-focus.directive';
import { AllowOnlyNumbersDirective } from './directives/allowNumbersOnly.directive';
import { DraggableDirective } from './directives/free-dragging.directive';
import { FocusTrapDirective } from './directives/focus-trap.directive';
import { GenericViewerTemplateComponent } from './generic-model-viewer/generic-viewer-template/generic-viewer-template.component';
import { VersionBadgeComponent } from './version-badge/version-badge.component';
import { MomentTimezonePipe } from './pipe/momentTimezone.pipe';
import { GridCustomPagerComponent } from './grid-custom-pager/grid-custom-pager.component';
import { MapComponent } from './map/map.component';
import { PopoverConfirmComponent } from './popover-confirm/popover-confirm.component';
import { SitePipePipe } from './pipe/site-pipe.pipe';
import { CopyToClipboardDirective } from './directives/copyToClipboard.directive';
import { CopyCellComponent } from './copy-cell/copy-cell.component';
import { GridSortHeaderComponent } from './grid-sort-header/grid-sort-header.component';
import { DrawerComponent } from './drawer/drawer.component';


@NgModule({
    declarations: [
        AccountEditComponent, ActionIconComponent, AlertComponent, AuditComponent, AuditPipe,
        AuthListenerComponent, AuthorityNamePipe, AutoApplyAuthoritiesDirective, ConfirmComponent, DatePickerComponent,
        Decimal2CorrectDirective, DraggableOrderedListInputComponent, ErrorModalComponent, ExportPdfComponent, ExportPdfModalComponent,
        FilteringButtonComponent, FiltersModalComponent, FormReadonlyModeDirective, HasAnyAuthorityDirective, InputReaderComponent,
        LoaderComponent, NotificationsComponent, NumberInputFormaterDirective, NumberInputSubmitEventTriggerDirective, PdfViewerComponent,
        PracticeComponent, ProtectedButtonLvl2Directive, ReadOnlyDirective, SafeHtmlPipe, SwitchComponent,
        TimePipe, TruncatePipe, TypeaheadComponent, TypeaheadScrollFixDirective, TypeaheadSharedDirective,
        ValidationOnBlurDirective, FormControlErrorDirective, NativeElementInjectorDirective, ValidatorErrorPipe,
        PhoneNumberComponent,
        UploadFileModalComponent,
        ButtonUploadFileComponent,
        FileUploadedComponent,
        DragAndDropDirective,
        PeriodSetterComponent,
        AllowLettersOnlyDirective,
        AutoFocusDirective,
        AllowOnlyNumbersDirective,
        DraggableDirective,
        FocusTrapDirective,
        GenericViewerTemplateComponent,
        VersionBadgeComponent,
        MomentTimezonePipe,
        GridCustomPagerComponent,
        MapComponent,
        PopoverConfirmComponent,
        SitePipePipe,CopyToClipboardDirective,
        CopyCellComponent,GridSortHeaderComponent,
        DrawerComponent


    ],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        FormsModule,
        BrowserAnimationsModule,
        NgbAlertModule, NgbPaginationModule, NgbTypeaheadModule, NgbToastModule, NgbModalModule, NgbDropdownModule,
        SharedRoutingModule,
        // required for the audit
        GridModule, SimplebarAngularModule,
        DragulaModule,
        NgbDatepickerModule, Select2Module, NgxMaskModule, NgbNavModule,
        NgbCollapseModule,LeafletModule

    ],
    exports: [
        ActionIconComponent, AlertComponent, AuditPipe, AuthListenerComponent, AuthorityNamePipe,
        AutoApplyAuthoritiesDirective, DatePickerComponent, Decimal2CorrectDirective, DraggableOrderedListInputComponent, ExportPdfComponent,
        ExportPdfModalComponent, FilteringButtonComponent, FormReadonlyModeDirective, HasAnyAuthorityDirective, LoaderComponent,
        NumberInputFormaterDirective, NumberInputSubmitEventTriggerDirective, PdfViewerComponent, PracticeComponent, ProtectedButtonLvl2Directive,
        ReadOnlyDirective, SafeHtmlPipe, SwitchComponent, TimePipe, TruncatePipe, FormControlErrorDirective,
        TypeaheadComponent, TypeaheadScrollFixDirective, TypeaheadSharedDirective, ValidationOnBlurDirective, NativeElementInjectorDirective, ValidatorErrorPipe, PhoneNumberComponent,
        ButtonUploadFileComponent,
        PeriodSetterComponent,
        AllowLettersOnlyDirective,
        AutoFocusDirective,
        AllowOnlyNumbersDirective,
        DraggableDirective,
        FocusTrapDirective,
        GenericViewerTemplateComponent,
        VersionBadgeComponent,
        MomentTimezonePipe,
        GridCustomPagerComponent,
        MapComponent,PopoverConfirmComponent,
        SitePipePipe,CopyToClipboardDirective,
        CopyCellComponent,GridSortHeaderComponent,
        DrawerComponent

    ],
    entryComponents: [
        ConfirmComponent, InputReaderComponent
    ],
    providers: [
        {
            provide: InjectableRxStompConfig,
            useValue: myRxStompConfig
        },
        {
            provide: RxStompService,
            useFactory: rxStompServiceFactory,
            deps: [InjectableRxStompConfig]
        },
        DragulaService,
        DatePipe,
        DecimalPipe,
        ValidatorErrorPipe
    ],

})
export class SharedModule { }
