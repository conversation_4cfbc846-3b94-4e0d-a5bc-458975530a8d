import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  HostListener,
  ElementRef,
  ViewChild,
  AfterViewInit,
  ContentChild
} from '@angular/core';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { DrawerPlacement, DrawerClosePosition } from './drawer.interface';

@Component({
  selector: 'app-drawer',
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.scss'],
  animations: [
    trigger('slideIn', [
      state('left-closed', style({ transform: 'translateX(-100%)' })),
      state('left-open', style({ transform: 'translateX(0)' })),
      state('right-closed', style({ transform: 'translateX(100%)' })),
      state('right-open', style({ transform: 'translateX(0)' })),
      state('top-closed', style({ transform: 'translateY(-100%)' })),
      state('top-open', style({ transform: 'translateY(0)' })),
      state('bottom-closed', style({ transform: 'translateY(100%)' })),
      state('bottom-open', style({ transform: 'translateY(0)' })),
      transition('*-closed => *-open', animate('300ms ease-out')),
      transition('*-open => *-closed', animate('250ms ease-in'))
    ]),
    trigger('fadeIn', [
      state('closed', style({ opacity: 0, visibility: 'hidden' })),
      state('open', style({ opacity: 1, visibility: 'visible' })),
      transition('closed => open', animate('200ms ease-out')),
      transition('open => closed', animate('150ms ease-in'))
    ])
  ]
})
export class DrawerComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() isOpen: boolean = false;
  @Input() placement: DrawerPlacement = 'left';
  @Input() width: string = '320px';
  @Input() height: string = '300px';
  @Input() header: string = '';
  @Input() closePosition: DrawerClosePosition = 'right';
  @Input() hideOverlay: boolean = false;
  @Input() backdrop: boolean | 'static' = true;
  @Input() keyboard: boolean = true;
  @Input() panelClass: string = '';
  @Input() backdropClass: string = '';
  @Input() disableClose: boolean = false;
  @Input() autoFocus: boolean = true;
  @Input() restoreFocus: boolean = true;

  @Output() isOpenChange = new EventEmitter<boolean>();
  @Output() closed = new EventEmitter<void>();
  @Output() opened = new EventEmitter<void>();

  @ViewChild('drawerPanel') drawerPanel!: ElementRef<HTMLDivElement>;
  @ViewChild('closeButton') closeButton!: ElementRef<HTMLButtonElement>;
  @ContentChild('[slot=footer]') footerContent: any;

  private focusedElementBeforeOpen?: HTMLElement;
  private focusableElements: HTMLElement[] = [];

  constructor(private elementRef: ElementRef) {}

  ngOnInit(): void {
    if (this.isOpen) {
      this.handleOpen();
    }
  }

  ngAfterViewInit(): void {
    this.updateFocusableElements();
  }

  ngOnDestroy(): void {
    this.restoreFocusIfNeeded();
    this.removeBodyScrollLock();
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent): void {
    if (!this.isOpen) return;

    switch (event.key) {
      case 'Escape':
        if (this.keyboard && !this.disableClose) {
          event.preventDefault();
          this.close();
        }
        break;
      case 'Tab':
        this.handleTabKey(event);
        break;
    }
  }

  @HostListener('click', ['$event'])
  handleBackdropClick(event: MouseEvent): void {
    if (!this.isOpen || this.backdrop === 'static' || this.disableClose) return;

    const target = event.target as HTMLElement;
    const isBackdrop = target.classList.contains('drawer-backdrop');
    
    if (isBackdrop && this.backdrop) {
      this.close();
    }
  }

  open(): void {
    if (this.isOpen) return;
    
    this.focusedElementBeforeOpen = document.activeElement as HTMLElement;
    this.isOpen = true;
    this.isOpenChange.emit(true);
    this.handleOpen();
    
    // Emit opened after animation completes
    setTimeout(() => {
      this.opened.emit();
      if (this.autoFocus) {
        this.setInitialFocus();
      }
    }, 300);
  }

  close(): void {
    if (!this.isOpen) return;
    
    this.isOpen = false;
    this.isOpenChange.emit(false);
    this.handleClose();
    
    // Emit closed after animation completes
    setTimeout(() => {
      this.closed.emit();
      this.restoreFocusIfNeeded();
    }, 250);
  }

  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  private handleOpen(): void {
    this.addBodyScrollLock();
    setTimeout(() => {
      this.updateFocusableElements();
    });
  }

  private handleClose(): void {
    this.removeBodyScrollLock();
  }

  private addBodyScrollLock(): void {
    if (!this.hideOverlay) {
      document.body.style.overflow = 'hidden';
    }
  }

  private removeBodyScrollLock(): void {
    if (!this.hideOverlay) {
      document.body.style.overflow = '';
    }
  }

  private updateFocusableElements(): void {
    const focusableSelectors = [
      'button:not([disabled]):not([tabindex="-1"])',
      '[href]:not([tabindex="-1"])',
      'input:not([disabled]):not([readonly]):not([tabindex="-1"])',
      'select:not([disabled]):not([tabindex="-1"])',
      'textarea:not([disabled]):not([readonly]):not([tabindex="-1"])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ');

    this.focusableElements = Array.from(
      this.elementRef.nativeElement.querySelectorAll(focusableSelectors)
    );
  }

  private setInitialFocus(): void {
    const firstFocusable = this.focusableElements[0];
    if (firstFocusable) {
      firstFocusable.focus();
    } else if (this.closeButton) {
      this.closeButton.nativeElement.focus();
    }
  }

  private restoreFocusIfNeeded(): void {
    if (this.restoreFocus && this.focusedElementBeforeOpen) {
      this.focusedElementBeforeOpen.focus();
    }
  }

  private handleTabKey(event: KeyboardEvent): void {
    if (this.focusableElements.length === 0) return;

    const firstElement = this.focusableElements[0];
    const lastElement = this.focusableElements[this.focusableElements.length - 1];
    const activeElement = document.activeElement as HTMLElement;

    if (event.shiftKey) {
      // Shift + Tab
      if (activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  get animationState(): string {
    return `${this.placement}-${this.isOpen ? 'open' : 'closed'}`;
  }

  get backdropAnimationState(): string {
    return this.isOpen ? 'open' : 'closed';
  }

  get drawerClasses(): string {
    const classes = ['drawer-panel', `drawer-${this.placement}`];
    
    if (this.panelClass) {
      classes.push(this.panelClass);
    }
    
    return classes.join(' ');
  }

  get drawerStyles(): any {
    const styles: any = {};
    
    if (this.placement === 'left' || this.placement === 'right') {
      if (this.width) {
        styles.width = this.width;
      }
    } else {
      if (this.height) {
        styles.height = this.height;
      }
    }
    
    return styles;
  }

  get backdropClasses(): string {
    const classes = ['drawer-backdrop'];
    
    if (this.backdropClass) {
      classes.push(this.backdropClass);
    }
    
    return classes.join(' ');
  }

  get showCloseButton(): boolean {
    return this.closePosition !== 'none' && !this.disableClose;
  }

  get closeButtonClasses(): string {
    const classes = ['drawer-close-button'];
    
    if (this.closePosition) {
      classes.push(`close-${this.closePosition}`);
    }
    
    return classes.join(' ');
  }

  get hasFooterContent(): boolean {
    return !!this.footerContent;
  }
}
