import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuardService } from '../services/auth-guard.service';
import { CanDeactivateGuard } from '../services/can-deactivate-guard.service';
import { MenuComponent } from './menu/menu.component';
import { MonitoringTailLogsComponent } from './monitoring-tail-logs/monitoring-tail-logs.component';
import { TestAsyncMessageComponent } from './test-async-message/test-async-message.component';
import { TestHabilitationsAutoComponent } from './test-habilitations-auto/test-habilitations-auto.component';
import { TestMasterDetailsComponent } from './test-master-details/test-master-details.component';
import { TestMasterComponent } from './test-master/test-master.component';
import { TestPrintComponent } from './test-print/test-print.component';
import { TestProductSearchWidgetComponent } from './test-product-search-widget/test-product-search-widget.component';
import { TestScannerComponent } from './test-scanner/test-scanner.component';
import { TestUploadComponent } from './test-upload/test-upload.component';
import { DrawerDemoComponent } from '../drawer/drawer-demo.component';


const routes: Routes = [
  {
    path: '',
    pathMatch: 'full'  ,
    redirectTo: 'menu'
  },
  {
    path: 'testhabilitationsauto',
    component: TestHabilitationsAutoComponent,
    pathMatch: 'full'  ,
    data: { create: false, breadcrumb: 'dummy'} ,
    canActivate: [AuthGuardService]
  },
  {
    path: 'testupload',
    component: TestUploadComponent,
    pathMatch: 'full'  ,
    canActivate: [AuthGuardService]
  },
  {
    path: 'testprint',
    component: TestPrintComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  {
    path: 'testmessage',
    component: TestAsyncMessageComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  {
    path: 'testmaster',
    component: TestMasterComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService],
    canDeactivate: [CanDeactivateGuard]
  },
  {
    path: 'testmasterdetails',
    component: TestMasterDetailsComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService],
  },
  {
    path: 'testproduct',
    component: TestProductSearchWidgetComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  
  {
    path: 'testscanner',
    component: TestScannerComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  
  {
    path: 'testlogtail',
    component: MonitoringTailLogsComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  
  {
    path: 'menu',
    component: MenuComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
  {
    path: 'drawer-demo',
    component: DrawerDemoComponent,
    pathMatch: 'full',
    canActivate: [AuthGuardService]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TestRoutingModule { }
