import { Component } from '@angular/core';
import { DrawerPlacement } from './drawer.interface';

@Component({
  selector: 'app-drawer-demo',
  template: `
    <div class="drawer-demo p-4">
      <h2>Custom Drawer Component Demo</h2>
      
      <div class="row mb-4">
        <div class="col-md-6">
          <h4>Basic Controls</h4>
          <div class="btn-group-vertical d-grid gap-2">
            <button class="btn btn-primary" (click)="leftDrawerOpen = true">
              Open Left Drawer
            </button>
            <button class="btn btn-secondary" (click)="rightDrawerOpen = true">
              Open Right Drawer
            </button>
            <button class="btn btn-success" (click)="topDrawerOpen = true">
              Open Top Drawer
            </button>
            <button class="btn btn-warning" (click)="bottomDrawerOpen = true">
              Open Bottom Drawer
            </button>
          </div>
        </div>
        
        <div class="col-md-6">
          <h4>Advanced Options</h4>
          <div class="btn-group-vertical d-grid gap-2">
            <button class="btn btn-info" (click)="customDrawerOpen = true">
              Custom Size Drawer
            </button>
            <button class="btn btn-dark" (click)="noBackdropDrawerOpen = true">
              No Backdrop Drawer
            </button>
            <button class="btn btn-outline-primary" (click)="staticDrawerOpen = true">
              Static Backdrop
            </button>
            <button class="btn btn-outline-success" (click)="footerDrawerOpen = true">
              Drawer with Footer
            </button>
          </div>
        </div>
      </div>

      <!-- Left Drawer -->
      <app-drawer 
        [(isOpen)]="leftDrawerOpen"
        placement="left"
        header="Navigation Menu"
        width="300px">
        
        <div class="p-3">
          <h5>Navigation</h5>
          <ul class="list-unstyled">
            <li><a href="#" class="text-decoration-none d-block py-1">Dashboard</a></li>
            <li><a href="#" class="text-decoration-none d-block py-1">Users</a></li>
            <li><a href="#" class="text-decoration-none d-block py-1">Settings</a></li>
            <li><a href="#" class="text-decoration-none d-block py-1">Reports</a></li>
          </ul>
        </div>
      </app-drawer>

      <!-- Right Drawer -->
      <app-drawer 
        [(isOpen)]="rightDrawerOpen"
        placement="right"
        header="Filters"
        width="350px"
        closePosition="left">
        
        <div class="p-3">
          <h5>Filter Options</h5>
          <form>
            <div class="mb-3">
              <label class="form-label">Date Range</label>
              <select class="form-select">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 90 days</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Category</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="cat1">
                <label class="form-check-label" for="cat1">Category 1</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="cat2">
                <label class="form-check-label" for="cat2">Category 2</label>
              </div>
            </div>
          </form>
        </div>
      </app-drawer>

      <!-- Top Drawer -->
      <app-drawer 
        [(isOpen)]="topDrawerOpen"
        placement="top"
        header="Notifications"
        height="250px">
        
        <div class="p-3">
          <div class="alert alert-info mb-2">
            <strong>New Message:</strong> You have 3 unread notifications.
          </div>
          <div class="alert alert-warning mb-2">
            <strong>System Update:</strong> Maintenance scheduled for tonight.
          </div>
          <div class="alert alert-success mb-0">
            <strong>Task Complete:</strong> Your report has been generated.
          </div>
        </div>
      </app-drawer>

      <!-- Bottom Drawer -->
      <app-drawer 
        [(isOpen)]="bottomDrawerOpen"
        placement="bottom"
        header="Quick Actions"
        height="200px">
        
        <div class="p-3">
          <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-sm btn-outline-primary">Create New</button>
            <button class="btn btn-sm btn-outline-secondary">Import Data</button>
            <button class="btn btn-sm btn-outline-success">Export Report</button>
            <button class="btn btn-sm btn-outline-warning">Settings</button>
            <button class="btn btn-sm btn-outline-danger">Delete Selected</button>
          </div>
        </div>
      </app-drawer>

      <!-- Custom Size Drawer -->
      <app-drawer 
        [(isOpen)]="customDrawerOpen"
        placement="left"
        header="Custom Size"
        width="500px">
        
        <div class="p-4">
          <h5>Wide Content Area</h5>
          <p>This drawer has a custom width of 500px, perfect for displaying more detailed content.</p>
          <div class="row">
            <div class="col-6">
              <h6>Column 1</h6>
              <p>Content for the first column with more space.</p>
            </div>
            <div class="col-6">
              <h6>Column 2</h6>
              <p>Content for the second column with more space.</p>
            </div>
          </div>
        </div>
      </app-drawer>

      <!-- No Backdrop Drawer -->
      <app-drawer 
        [(isOpen)]="noBackdropDrawerOpen"
        placement="right"
        header="No Backdrop"
        [hideOverlay]="true"
        width="280px">
        
        <div class="p-3">
          <h5>No Backdrop</h5>
          <p>This drawer doesn't have a backdrop overlay.</p>
          <div class="alert alert-info">
            <small>You can still interact with the page behind this drawer.</small>
          </div>
        </div>
      </app-drawer>

      <!-- Static Backdrop Drawer -->
      <app-drawer 
        [(isOpen)]="staticDrawerOpen"
        placement="right"
        header="Static Backdrop"
        [backdrop]="'static'"
        [keyboard]="false"
        width="320px">
        
        <div class="p-3">
          <h5>Static Backdrop</h5>
          <p>This drawer has a static backdrop and disabled keyboard shortcuts.</p>
          <div class="alert alert-warning">
            <small><strong>Note:</strong> ESC key and backdrop clicks are disabled.</small>
          </div>
        </div>
      </app-drawer>

      <!-- Drawer with Footer -->
      <app-drawer 
        [(isOpen)]="footerDrawerOpen"
        placement="left"
        header="Form Example"
        width="400px">
        
        <!-- Custom Header Content -->
        <div slot="header" class="text-muted">
          <small>Fill out the form below</small>
        </div>
        
        <!-- Main Content -->
        <div class="p-3">
          <form>
            <div class="mb-3">
              <label class="form-label">Name</label>
              <input type="text" class="form-control" placeholder="Enter your name">
            </div>
            <div class="mb-3">
              <label class="form-label">Email</label>
              <input type="email" class="form-control" placeholder="Enter your email">
            </div>
            <div class="mb-3">
              <label class="form-label">Message</label>
              <textarea class="form-control" rows="4" placeholder="Enter your message"></textarea>
            </div>
          </form>
        </div>
        
        <!-- Footer Content -->
        <div slot="footer" class="d-flex gap-2 justify-content-end">
          <button type="button" class="btn btn-secondary" (click)="footerDrawerOpen = false">
            Cancel
          </button>
          <button type="button" class="btn btn-primary">
            Submit
          </button>
        </div>
      </app-drawer>

    </div>
  `,
  styles: [`
    .drawer-demo {
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .btn-group-vertical .btn {
      margin-bottom: 0.5rem;
    }
  `]
})
export class DrawerDemoComponent {
  leftDrawerOpen = false;
  rightDrawerOpen = false;
  topDrawerOpen = false;
  bottomDrawerOpen = false;
  customDrawerOpen = false;
  noBackdropDrawerOpen = false;
  staticDrawerOpen = false;
  footerDrawerOpen = false;
}
