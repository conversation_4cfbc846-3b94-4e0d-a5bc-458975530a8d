<!-- Drawer Container -->
<div class="drawer-container" 
     [class.drawer-open]="isOpen">

  <!-- Backdrop -->
  <div *ngIf="!hideOverlay && isOpen"
       [class]="backdropClasses"
       [@fadeIn]="backdropAnimationState"
       (click)="handleBackdropClick($event)">
  </div>

  <!-- Drawer Panel -->
  <div #drawerPanel
       [class]="drawerClasses"
       [style]="drawerStyles"
       [@slideIn]="animationState"
       role="dialog"
       [attr.aria-modal]="!hideOverlay"
       [attr.aria-labelledby]="header ? 'drawer-title' : null"
       tabindex="-1">

    <!-- Header -->
    <div *ngIf="header || showCloseButton" 
         class="drawer-header">
      
      <!-- Header Content -->
      <div *ngIf="header" 
           class="drawer-title"
           id="drawer-title">
        {{ header }}
      </div>

      <!-- Header Content Projection -->
      <ng-content select="[slot=header]"></ng-content>

      <!-- Close Button -->
      <button *ngIf="showCloseButton"
              #closeButton
              type="button"
              [class]="closeButtonClasses"
              [attr.aria-label]="'Close drawer'"
              (click)="close()">
        <i class="mdi mdi-close" aria-hidden="true"></i>
      </button>
    </div>

    <!-- Content -->
    <div class="drawer-content">
      <ng-content></ng-content>
    </div>

    <!-- Footer Content Projection -->
    <div *ngIf="hasFooterContent" class="drawer-footer">
      <ng-content select="[slot=footer]"></ng-content>
    </div>

  </div>
</div>
