// Drawer Component Styles with Animations
// Based on NewsKit drawer guidelines

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1050;
  pointer-events: none;
  
  &.drawer-open {
    pointer-events: auto;
  }
}

// Backdrop
.drawer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

// Drawer Panel Base
.drawer-panel {
  position: absolute;
  background-color: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1051;
  
  // Focus outline for accessibility
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
  }
}

// Placement Styles
.drawer-left {
  top: 0;
  left: 0;
  height: 100%;
  width: 320px;
}

.drawer-right {
  top: 0;
  right: 0;
  height: 100%;
  width: 320px;
}

.drawer-top {
  top: 0;
  left: 0;
  width: 100%;
  height: 300px;
}

.drawer-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 300px;
}

// Header
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  flex-shrink: 0;
  min-height: 60px;
  gap: 1rem;
}

.drawer-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #212529;
  margin: 0;
  line-height: 1.2;
  flex: 1;
}

// Close Button
.drawer-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background-color: transparent;
  color: #6c757d;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.15s ease-in-out;
  flex-shrink: 0;
  
  &:hover {
    background-color: #e9ecef;
    color: #495057;
  }
  
  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    background-color: #e9ecef;
  }
  
  &:active {
    background-color: #dee2e6;
  }
  
  i {
    font-size: 18px;
    line-height: 1;
  }
}

// Close button positioning
.close-left {
  order: -1;
}

.close-right {
  order: 1;
}

// Content
.drawer-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

// Footer
.drawer-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

// Animation Enhancement Classes
.drawer-panel {
  // Smooth transitions for size changes
  transition: width 0.3s ease-out, height 0.3s ease-out;
}

// Responsive Design
@media (max-width: 768px) {
  .drawer-left,
  .drawer-right {
    width: 280px;
  }
  
  .drawer-top,
  .drawer-bottom {
    height: 250px;
  }
  
  .drawer-header {
    padding: 0.75rem 1rem;
    min-height: 50px;
  }
  
  .drawer-title {
    font-size: 1.1rem;
  }
  
  .drawer-content,
  .drawer-footer {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .drawer-left,
  .drawer-right {
    width: 100%;
    max-width: 320px;
  }
  
  .drawer-header {
    padding: 0.5rem 0.75rem;
  }
  
  .drawer-content,
  .drawer-footer {
    padding: 0.75rem;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .drawer-panel {
    border: 2px solid #000000;
  }
  
  .drawer-header,
  .drawer-footer {
    border-color: #000000;
  }
  
  .drawer-close-button {
    border: 1px solid #000000;
    
    &:focus {
      outline: 3px solid #000000;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .drawer-panel {
    transition: none;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .drawer-panel {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .drawer-header,
  .drawer-footer {
    background-color: #1a202c;
    border-color: #4a5568;
  }
  
  .drawer-title {
    color: #e2e8f0;
  }
  
  .drawer-close-button {
    color: #a0aec0;
    
    &:hover {
      background-color: #4a5568;
      color: #e2e8f0;
    }
    
    &:focus {
      background-color: #4a5568;
    }
  }
  
  .drawer-content {
    &::-webkit-scrollbar-track {
      background: #2d3748;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #4a5568;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #718096;
    }
  }
}

// Animation states for better performance
.drawer-panel {
  will-change: transform;
  backface-visibility: hidden;
}

.drawer-backdrop {
  will-change: opacity;
  backface-visibility: hidden;
}
