# Custom Drawer Component

A simple, accessible drawer component with content projection and smooth animations.

## Features

- ✅ **Multiple Placements**: Left, Right, Top, Bottom
- ✅ **Content Projection**: Use `<ng-content>` for flexible content
- ✅ **Smooth Animations**: CSS animations with Angular Animations API
- ✅ **Accessibility**: Focus management, keyboard navigation, ARIA support
- ✅ **Responsive Design**: Mobile-friendly
- ✅ **Customizable**: Width, height, styling options

## Basic Usage

```html
<app-drawer 
  [(isOpen)]="drawerOpen"
  placement="left"
  header="My Drawer"
  width="300px">
  
  <p>Your content goes here!</p>
  
</app-drawer>
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `isOpen` | `boolean` | `false` | Controls drawer visibility (two-way binding) |
| `placement` | `'left' \| 'right' \| 'top' \| 'bottom'` | `'left'` | Drawer position |
| `width` | `string` | `'320px'` | Width for left/right drawers |
| `height` | `string` | `'300px'` | Height for top/bottom drawers |
| `header` | `string` | `''` | Header text |
| `closePosition` | `'left' \| 'right' \| 'none'` | `'right'` | Close button position |
| `hideOverlay` | `boolean` | `false` | Hide backdrop overlay |
| `backdrop` | `boolean \| 'static'` | `true` | Backdrop behavior |
| `keyboard` | `boolean` | `true` | Enable ESC key to close |
| `panelClass` | `string` | `''` | Custom CSS class for panel |
| `backdropClass` | `string` | `''` | Custom CSS class for backdrop |
| `disableClose` | `boolean` | `false` | Disable all close methods |
| `autoFocus` | `boolean` | `true` | Auto-focus first element |
| `restoreFocus` | `boolean` | `true` | Restore focus when closed |

## Output Events

| Event | Type | Description |
|-------|------|-------------|
| `openChange` | `EventEmitter<boolean>` | Emitted when open state changes |
| `opened` | `EventEmitter<void>` | Emitted after drawer opens |
| `closed` | `EventEmitter<void>` | Emitted after drawer closes |

## Content Projection

### Basic Content
```html
<app-drawer [(isOpen)]="isOpen">
  <p>Main content goes here</p>
</app-drawer>
```

### Header Content
```html
<app-drawer [(isOpen)]="isOpen" header="Title">
  <div slot="header">
    <small>Additional header content</small>
  </div>
  <p>Main content</p>
</app-drawer>
```

### Footer Content
```html
<app-drawer [(isOpen)]="isOpen">
  <p>Main content</p>
  <div slot="footer">
    <button class="btn btn-primary">Save</button>
    <button class="btn btn-secondary">Cancel</button>
  </div>
</app-drawer>
```

## Examples

### Navigation Drawer
```html
<app-drawer 
  [(isOpen)]="navOpen"
  placement="left"
  header="Navigation"
  width="280px">
  
  <nav>
    <a href="/dashboard">Dashboard</a>
    <a href="/users">Users</a>
    <a href="/settings">Settings</a>
  </nav>
</app-drawer>
```

### Filter Drawer
```html
<app-drawer 
  [(isOpen)]="filterOpen"
  placement="right"
  header="Filters"
  width="350px">
  
  <form>
    <div class="form-group">
      <label>Date Range</label>
      <select class="form-control">
        <option>Last 7 days</option>
        <option>Last 30 days</option>
      </select>
    </div>
  </form>
</app-drawer>
```

### Notification Drawer
```html
<app-drawer 
  [(isOpen)]="notifOpen"
  placement="top"
  header="Notifications"
  height="200px">
  
  <div class="notification-list">
    <div class="alert alert-info">New message received</div>
    <div class="alert alert-warning">System update available</div>
  </div>
</app-drawer>
```

## Styling

The component includes comprehensive SCSS with:
- Responsive breakpoints
- Dark mode support
- High contrast mode support
- Reduced motion support
- Custom scrollbars

### Custom Styling
```scss
// Override default styles
.my-custom-drawer {
  .drawer-panel {
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .drawer-header {
    background: #007bff;
    color: white;
  }
}
```

## Accessibility

- **Focus Management**: Automatic focus handling and restoration
- **Keyboard Navigation**: ESC to close, Tab trapping within drawer
- **ARIA Support**: Proper roles, labels, and states
- **Screen Reader**: Compatible with screen readers

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Demo

Visit `/test/drawer-demo` to see interactive examples of all drawer features.
