import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DrawerComponent } from './drawer.component';

describe('DrawerComponent', () => {
  let component: DrawerComponent;
  let fixture: ComponentFixture<DrawerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DrawerComponent],
      imports: [BrowserAnimationsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(DrawerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isOpen).toBeFalse();
    expect(component.placement).toBe('left');
    expect(component.width).toBe('320px');
    expect(component.height).toBe('300px');
  });

  it('should open drawer and emit events', () => {
    spyOn(component.isOpenChange, 'emit');
    spyOn(component.opened, 'emit');

    component.open();

    expect(component.isOpen).toBeTrue();
    expect(component.isOpenChange.emit).toHaveBeenCalledWith(true);
    
    // Test opened event after timeout
    setTimeout(() => {
      expect(component.opened.emit).toHaveBeenCalled();
    }, 300);
  });

  it('should close drawer and emit events', () => {
    component.isOpen = true;
    spyOn(component.isOpenChange, 'emit');
    spyOn(component.closed, 'emit');

    component.close();

    expect(component.isOpen).toBeFalse();
    expect(component.isOpenChange.emit).toHaveBeenCalledWith(false);
    
    // Test closed event after timeout
    setTimeout(() => {
      expect(component.closed.emit).toHaveBeenCalled();
    }, 250);
  });

  it('should toggle drawer state', () => {
    expect(component.isOpen).toBeFalse();
    
    component.toggle();
    expect(component.isOpen).toBeTrue();
    
    component.toggle();
    expect(component.isOpen).toBeFalse();
  });

  it('should generate correct animation state', () => {
    component.placement = 'right';
    component.isOpen = false;
    expect(component.animationState).toBe('right-closed');
    
    component.isOpen = true;
    expect(component.animationState).toBe('right-open');
  });

  it('should handle keyboard events', () => {
    spyOn(component, 'close');
    component.isOpen = true;
    component.keyboard = true;
    component.disableClose = false;
    
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
    spyOn(escapeEvent, 'preventDefault');
    
    component.handleKeyDown(escapeEvent);
    
    expect(escapeEvent.preventDefault).toHaveBeenCalled();
    expect(component.close).toHaveBeenCalled();
  });

  it('should show close button when appropriate', () => {
    component.closePosition = 'right';
    component.disableClose = false;
    expect(component.showCloseButton).toBeTrue();
    
    component.closePosition = 'none';
    expect(component.showCloseButton).toBeFalse();
    
    component.closePosition = 'right';
    component.disableClose = true;
    expect(component.showCloseButton).toBeFalse();
  });
});
