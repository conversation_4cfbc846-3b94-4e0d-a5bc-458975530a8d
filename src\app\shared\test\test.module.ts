import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TestRoutingModule } from './test-routing.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgbAccordionModule, NgbDropdownModule, NgbPaginationModule, NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '../shared.module';
import { HttpClientModule } from '@angular/common/http';
import { TestPrintComponent } from './test-print/test-print.component';
import { TestAsyncMessageComponent } from './test-async-message/test-async-message.component';
import { TestMasterComponent } from './test-master/test-master.component';
import { TestUploadComponent } from './test-upload/test-upload.component';
import { TestProductSearchWidgetComponent } from './test-product-search-widget/test-product-search-widget.component';
import { GridModule } from '@progress/kendo-angular-grid';

import { NgxMaskModule } from 'ngx-mask';
import { TestScannerComponent } from './test-scanner/test-scanner.component';
import { MenuComponent } from './menu/menu.component';
import { TestMasterDetailsComponent } from './test-master-details/test-master-details.component'
import { TestHabilitationsAutoComponent } from './test-habilitations-auto/test-habilitations-auto.component';
import { MonitoringTailLogsComponent } from './monitoring-tail-logs/monitoring-tail-logs.component';
import { DrawerDemoComponent } from '../drawer/drawer-demo.component';

@NgModule({
  declarations: [TestUploadComponent, TestPrintComponent, TestAsyncMessageComponent,
    TestMasterComponent, TestProductSearchWidgetComponent, TestScannerComponent,
    MenuComponent, TestMasterDetailsComponent, TestHabilitationsAutoComponent, MonitoringTailLogsComponent,
    DrawerDemoComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule, 
		FormsModule,
		NgbDropdownModule,
		NgbPaginationModule,
    NgbTypeaheadModule,
    NgbAccordionModule,
    HttpClientModule,

    NgxMaskModule.forRoot(),
    
    GridModule,
    SharedModule,
    TestRoutingModule
  ]
})
export class TestModule { }
